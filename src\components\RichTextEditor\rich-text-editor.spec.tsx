import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { RichTextEditor, ITextAreaProps } from './RichTextEditor';

// Mock TextArea component from @ceridianhcm/components

describe('RichTextEditor', () => {
  const defaultProps: ITextAreaProps = {
    id: 'test-rich-text-editor',
    value: 'Initial content',
    onChange: jest.fn(),
    label: 'Rich Text Editor',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders with required props', () => {
    render(<RichTextEditor {...defaultProps} />);
    const input = screen.getByTestId(`${defaultProps.id}-input`);
    expect(input).toBeInTheDocument();
    expect(input).toHaveValue('Initial content');
  });

  it('handles value changes', async () => {
    const handleChange = jest.fn();
    render(<RichTextEditor {...defaultProps} value="" onChange={handleChange} />);
    const input = screen.getByTestId(`${defaultProps.id}-input`);

    await userEvent.type(input, 'new text');
    expect(handleChange).toHaveBeenLastCalledWith('t');
  });

  it('respects disabled state', () => {
    render(<RichTextEditor {...defaultProps} disabled />);
    const input = screen.getByTestId(`${defaultProps.id}-input`);
    expect(input).toBeDisabled();
  });

  it('allows passing a custom testId', () => {
    const testId = 'custom-test-id';
    render(<RichTextEditor {...defaultProps} testId={testId} />);
    const input = screen.getByTestId(testId);
    expect(input).toBeInTheDocument();
  });

  it('forwards ref correctly', async () => {
    const ref = React.createRef<HTMLTextAreaElement>();
    render(<RichTextEditor {...defaultProps} ref={ref} />);

    // Wait for ref to be attached
    await new Promise((resolve) => {
      setTimeout(resolve, 0);
    });

    expect(ref.current).toBeInstanceOf(HTMLTextAreaElement);
  });
});
