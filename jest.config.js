/* eslint-env node */

module.exports = {
  // TypeScript support
  preset: 'ts-jest',

  // React DOM testing environment
  testEnvironment: 'jsdom',

  // Test setup file
  setupFilesAfterEnv: ['./jest.setup.ts'],

  // Module path mapping
  moduleNameMapper: {
    // Style files
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',

    // Path aliases (only include ones actually used in your codebase)
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@components/(.*)$': '<rootDir>/src/components/$1',
    '^@features/(.*)$': '<rootDir>/src/features/$1',
    '^@utils/(.*)$': '<rootDir>/src/utils/$1',
    '^@hooks/(.*)$': '<rootDir>/src/hooks/$1',
    '^@api$': '<rootDir>/src/api',
    '^@store/(.*)$': '<rootDir>/src/store/$1',
    '^@types/(.*)$': '<rootDir>/src/types/$1',
    '^@mocks/(.*)$': '<rootDir>/src/mocks/$1',
  },

  // Ignore patterns
  testPathIgnorePatterns: ['/node_modules/', '/playwright/'],

  // Coverage configuration (only if using coverage)
  coverageProvider: 'v8',
  coverageReporters: ['json', 'lcov', 'cobertura'],

  // CI/CD reporting (only if needed)
  reporters: ['default', ['jest-junit', { outputName: 'unittest-junit.xml' }]],
};
