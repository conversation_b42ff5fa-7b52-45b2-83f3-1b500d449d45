{"name": "hr-profile-ui", "version": "1.0.108", "description": "The hr-profile-ui microfrontend project.", "main": "src/main.tsx", "packageManager": "pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977", "scripts": {"analyze": "webpack --config webpack.prod.js --env target=dayforce analyzer=true", "start": "webpack serve --config webpack.dev.js --env target=dayforce mode=development --port 8004", "test": "jest", "test-generate-output": "jest --json --outputFile=.jest-test-results.json --coverage || true", "lint": "eslint . --ext .ts,.tsx", "lint-fix": "eslint . --ext .ts,.tsx --fix", "format": "prettier --write .", "standalone": "webpack serve --config webpack.dev.js --env target=standalone mode=development --port 8004", "build": "webpack --config webpack.dev.js --env target=dayforce mode=development", "build-prod": "webpack --config webpack.prod.js --env target=dayforce", "build-standalone": "webpack --config webpack.prod.js --env target=standalone mode=production", "serve": "http-server ./dist -p 8004", "prepare": "husky install"}, "lint-staged": {"*": ["prettier --write"]}, "repository": {"type": "git", "url": "https://dev.azure.com/Ceridian/Platform/_git/hr-profile-ui"}, "author": "Dayforce Platform Team", "license": "ISC", "devDependencies": {"@ceridianhcm/eslint-config-platform": "^1.27.16", "@ceridianhcm/microfrontend-harness": "^1.10.37", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^11.2.6", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^14.4.3", "@types/dayforce-common": "^1.26.3", "@types/dayforce-fallback": "^1.26.5", "@types/jest": "^29.5.11", "@types/jest-axe": "^3.5.9", "@types/node": "^22.15.21", "@types/react": "17.0.1", "@types/react-dom": "17.0.1", "@types/react-router-dom": "^5.3.3", "@types/react-test-renderer": "17.0.1", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "clean-webpack-plugin": "^3.0.0", "copy-webpack-plugin": "^8.1.1", "css-loader": "^5.2.4", "dotenv": "^16.5.0", "eslint": "^8.54.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-airbnb-typescript": "^17.1.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-filename-rules": "^1.3.1", "eslint-plugin-html": "^7.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-jsx-a11y": "^6.8.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-testing-library": "^6.2.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.3.1", "http-proxy-middleware": "^2.0.1", "http-server": "^0.12.3", "husky": "^7.0.4", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-axe": "^9.0.0", "jest-environment-jsdom": "^29.7.0", "jest-junit": "^13.0.0", "lint-staged": "^11.2.6", "mini-css-extract-plugin": "^1.5.0", "prettier": ">= 3.1.0", "react-refresh": "^0.11.0", "react-refresh-typescript": "^2.0.3", "rimraf": "^3.0.2", "sass": "^1.32.11", "sass-loader": "^11.0.1", "style-loader": "^2.0.0", "ts-jest": "^29.1.1", "ts-loader": "^9.0.2", "typescript": "^5.2.1", "url": "^0.11.0", "webpack": "^5.53.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^4.8.0", "webpack-dev-server": "^4.2.1", "webpack-merge": "^5.8.0", "whatwg-fetch": "3.6.2"}, "dependencies": {"@ceridianhcm/analytics": "^1.32.2", "@ceridianhcm/components": "^1.43.123", "@ceridianhcm/everest-cdk": "^1.43.123", "@ceridianhcm/globalization": "^1.32.5", "@ceridianhcm/platform-df-assets": "^1.33.2", "@ceridianhcm/platform-state": "^1.33.25", "@ceridianhcm/react-core": "^1.32.8", "@ceridianhcm/theme": "^1.43.3", "@hookform/resolvers": "^3.3.2", "@platform/catalog": "^1.32.9", "@platform/component": "^1.32.9", "@platform/core": "^1.27", "@platform/dayforce": "^1.32.9", "@platform/theme": "^1.32.1", "classnames": "^2.5.1", "react-hook-form": "^7.58.0", "react-router-dom": "^6.30.0", "xss": "^1.0.15", "zod": "^3.22.4"}, "peerDependencies": {"react": "17.0.1", "react-dom": "17.0.1"}}